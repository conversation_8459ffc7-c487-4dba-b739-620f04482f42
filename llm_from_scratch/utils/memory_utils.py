"""
Memory management utilities for efficient training in resource-constrained environments.
"""

import torch
import gc
import psutil
import os
from typing import Dict, Any
import logging


def get_memory_usage() -> Dict[str, Any]:
    """
    Get current memory usage statistics.
    
    Returns:
        Dictionary containing memory usage information
    """
    memory_info = {}
    
    # System memory
    system_memory = psutil.virtual_memory()
    memory_info['system'] = {
        'total_gb': system_memory.total / (1024**3),
        'available_gb': system_memory.available / (1024**3),
        'used_gb': system_memory.used / (1024**3),
        'percent': system_memory.percent
    }
    
    # GPU memory (if available)
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.memory_stats()
        memory_info['gpu'] = {
            'allocated_gb': torch.cuda.memory_allocated() / (1024**3),
            'reserved_gb': torch.cuda.memory_reserved() / (1024**3),
            'max_allocated_gb': torch.cuda.max_memory_allocated() / (1024**3),
            'max_reserved_gb': torch.cuda.max_memory_reserved() / (1024**3)
        }
    
    return memory_info


def clear_cache() -> None:
    """
    Clear various caches to free up memory.
    """
    # Clear Python garbage collection
    gc.collect()
    
    # Clear CUDA cache if available
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.reset_peak_memory_stats()


def log_memory_usage(logger: logging.Logger, prefix: str = "") -> None:
    """
    Log current memory usage.
    
    Args:
        logger: Logger instance
        prefix: Optional prefix for log message
    """
    memory_info = get_memory_usage()
    
    # Log system memory
    sys_mem = memory_info['system']
    logger.info(
        f"{prefix}System Memory: "
        f"{sys_mem['used_gb']:.2f}GB / {sys_mem['total_gb']:.2f}GB "
        f"({sys_mem['percent']:.1f}%)"
    )
    
    # Log GPU memory if available
    if 'gpu' in memory_info:
        gpu_mem = memory_info['gpu']
        logger.info(
            f"{prefix}GPU Memory: "
            f"Allocated: {gpu_mem['allocated_gb']:.2f}GB, "
            f"Reserved: {gpu_mem['reserved_gb']:.2f}GB"
        )


def check_memory_constraints(
    model: torch.nn.Module,
    batch_size: int,
    seq_length: int,
    logger: logging.Logger
) -> bool:
    """
    Check if current memory usage allows for training with given parameters.
    
    Args:
        model: PyTorch model
        batch_size: Training batch size
        seq_length: Sequence length
        logger: Logger instance
        
    Returns:
        True if memory constraints are satisfied
    """
    memory_info = get_memory_usage()
    
    # Estimate memory requirements
    model_params = sum(p.numel() for p in model.parameters())
    model_memory_gb = model_params * 4 / (1024**3)  # float32
    
    # Rough estimate of activation memory
    activation_memory_gb = (
        batch_size * seq_length * model.config.d_model * 4 / (1024**3)
    )
    
    total_estimated_gb = model_memory_gb * 3 + activation_memory_gb  # 3x for gradients
    
    if torch.cuda.is_available():
        # Check GPU memory
        gpu_total = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        gpu_available = gpu_total - memory_info['gpu']['allocated_gb']
        
        if total_estimated_gb > gpu_available * 0.8:  # Leave 20% buffer
            logger.warning(
                f"Estimated memory usage ({total_estimated_gb:.2f}GB) "
                f"may exceed available GPU memory ({gpu_available:.2f}GB)"
            )
            return False
    else:
        # Check system memory
        sys_available = memory_info['system']['available_gb']
        
        if total_estimated_gb > sys_available * 0.8:
            logger.warning(
                f"Estimated memory usage ({total_estimated_gb:.2f}GB) "
                f"may exceed available system memory ({sys_available:.2f}GB)"
            )
            return False
    
    return True


def optimize_for_colab() -> Dict[str, Any]:
    """
    Apply optimizations specific to Google Colab environment.
    
    Returns:
        Dictionary of applied optimizations
    """
    optimizations = {}
    
    # Set environment variables for memory optimization
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'
    optimizations['cuda_alloc_conf'] = 'max_split_size_mb:128'
    
    # Enable memory efficient attention if available
    try:
        torch.backends.cuda.enable_flash_sdp(True)
        optimizations['flash_attention'] = True
    except:
        optimizations['flash_attention'] = False
    
    # Clear initial cache
    clear_cache()
    optimizations['initial_cache_cleared'] = True
    
    return optimizations
