"""
Logging utilities for training and debugging.
"""

import logging
import sys
from typing import Optional
import torch
import torch.nn as nn
from datetime import datetime
import os


def setup_logger(
    name: str = "llm_training",
    log_file: Optional[str] = None,
    level: int = logging.INFO
) -> logging.Logger:
    """
    Set up a logger with both console and file output.
    
    Args:
        name: Logger name
        log_file: Optional log file path
        level: Logging level
        
    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler (if specified)
    if log_file:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def log_model_info(model: nn.Module, logger: logging.Logger) -> None:
    """
    Log detailed information about the model.
    
    Args:
        model: PyTorch model
        logger: Logger instance
    """
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    logger.info(f"Model Information:")
    logger.info(f"  Total parameters: {total_params:,}")
    logger.info(f"  Trainable parameters: {trainable_params:,}")
    logger.info(f"  Model size: {total_params * 4 / 1024**2:.2f} MB (float32)")
    
    # Log model architecture
    logger.info(f"Model Architecture:")
    for name, module in model.named_modules():
        if len(list(module.children())) == 0:  # Leaf modules only
            logger.info(f"  {name}: {module}")


def log_training_step(
    step: int,
    loss: float,
    lr: float,
    grad_norm: float,
    logger: logging.Logger,
    log_interval: int = 100
) -> None:
    """
    Log training step information.
    
    Args:
        step: Current training step
        loss: Current loss value
        lr: Current learning rate
        grad_norm: Gradient norm
        logger: Logger instance
        log_interval: How often to log
    """
    if step % log_interval == 0:
        logger.info(
            f"Step {step}: loss={loss:.4f}, lr={lr:.2e}, "
            f"grad_norm={grad_norm:.4f}"
        )


def log_evaluation_results(
    step: int,
    eval_loss: float,
    perplexity: float,
    logger: logging.Logger
) -> None:
    """
    Log evaluation results.
    
    Args:
        step: Current training step
        eval_loss: Evaluation loss
        perplexity: Model perplexity
        logger: Logger instance
    """
    logger.info(
        f"Evaluation at step {step}: "
        f"eval_loss={eval_loss:.4f}, perplexity={perplexity:.2f}"
    )
