"""
LLM From Scratch - A Complete Large Language Model Implementation

This package provides a complete implementation of a transformer-based
Large Language Model built from scratch for educational purposes.

Author: Educational Project
License: MIT
"""

__version__ = "0.1.0"
__author__ = "Educational Project"

# Core components
from .models import *
from .tokenizer import *
from .training import *
from .utils import *

__all__ = [
    "TransformerModel",
    "BPETokenizer", 
    "Trainer",
    "DataLoader",
    "Config"
]
