#!/usr/bin/env python3
"""
Quick start script for LLM training.

This script provides a simple way to start training with minimal configuration.
Perfect for getting started quickly in Colab or local environments.
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

import torch
import logging
from config import TINY_CONFIG, SMALL_CONFIG, DEFAULT_CONFIG
from llm_from_scratch.utils import setup_logger, get_memory_usage, optimize_for_colab


def main():
    """Quick start training pipeline."""
    
    # Setup logging
    logger = setup_logger("quick_start", log_file="logs/quick_start.log")
    logger.info("Starting LLM training quick start...")
    
    # Check environment
    logger.info(f"Python version: {sys.version}")
    logger.info(f"PyTorch version: {torch.__version__}")
    logger.info(f"CUDA available: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        logger.info(f"CUDA device: {torch.cuda.get_device_name(0)}")
    
    # Apply Colab optimizations
    logger.info("Applying memory optimizations...")
    optimizations = optimize_for_colab()
    for key, value in optimizations.items():
        logger.info(f"  {key}: {value}")
    
    # Log memory usage
    memory_info = get_memory_usage()
    logger.info("Memory usage:")
    logger.info(f"  System: {memory_info['system']['used_gb']:.1f}GB / {memory_info['system']['total_gb']:.1f}GB")
    
    if 'gpu' in memory_info:
        logger.info(f"  GPU: {memory_info['gpu']['allocated_gb']:.1f}GB allocated")
    
    # Choose configuration based on available memory
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        if gpu_memory >= 12:
            config = SMALL_CONFIG
            logger.info("Using SMALL_CONFIG (sufficient GPU memory)")
        else:
            config = TINY_CONFIG
            logger.info("Using TINY_CONFIG (limited GPU memory)")
    else:
        config = TINY_CONFIG
        logger.info("Using TINY_CONFIG (CPU training)")
    
    # Log configuration
    logger.info("Training configuration:")
    logger.info(f"  Model size: {config.d_model}")
    logger.info(f"  Layers: {config.n_layers}")
    logger.info(f"  Heads: {config.n_heads}")
    logger.info(f"  Batch size: {config.batch_size}")
    logger.info(f"  Max steps: {config.max_steps}")
    
    # Save configuration
    config_path = "config/current_config.json"
    config.save(config_path)
    logger.info(f"Configuration saved to {config_path}")
    
    logger.info("Quick start setup completed!")
    logger.info("Next steps:")
    logger.info("1. Run data preprocessing: python scripts/preprocess_data.py")
    logger.info("2. Train tokenizer: python scripts/train_tokenizer.py")
    logger.info("3. Start training: python scripts/train_model.py")
    logger.info("4. Or use the Jupyter notebooks in the notebooks/ directory")


if __name__ == "__main__":
    main()
