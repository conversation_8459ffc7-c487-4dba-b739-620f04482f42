#!/usr/bin/env python3
"""
Setup script for Google Colab environment.

This script prepares the Colab environment for LLM training by:
1. Installing required packages
2. Setting up directories
3. Configuring memory optimizations
4. Downloading sample data
"""

import os
import sys
import subprocess
import logging
from pathlib import Path


def setup_logging():
    """Setup basic logging."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def install_packages(logger):
    """Install required packages."""
    logger.info("Installing required packages...")
    
    # Core packages that might not be in Colab
    packages = [
        "tokenizers>=0.13.0",
        "datasets>=2.0.0", 
        "wandb>=0.15.0",
        "psutil>=5.8.0",
        "jsonlines>=3.0.0"
    ]
    
    for package in packages:
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package
            ])
            logger.info(f"Successfully installed {package}")
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to install {package}: {e}")


def setup_directories(logger):
    """Create necessary directories."""
    logger.info("Setting up directories...")
    
    directories = [
        "data/raw",
        "data/processed", 
        "data/samples",
        "checkpoints",
        "logs",
        "tokenizer",
        "outputs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"Created directory: {directory}")


def configure_environment(logger):
    """Configure environment variables and settings."""
    logger.info("Configuring environment...")
    
    # Memory optimization for CUDA
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'
    
    # Disable tokenizers parallelism warning
    os.environ['TOKENIZERS_PARALLELISM'] = 'false'
    
    # Set cache directories
    os.environ['HF_HOME'] = '/content/hf_cache'
    os.environ['TRANSFORMERS_CACHE'] = '/content/transformers_cache'
    
    logger.info("Environment configured for Colab")


def download_sample_data(logger):
    """Download small sample dataset for testing."""
    logger.info("Downloading sample data...")
    
    # Create a simple sample dataset
    sample_texts = [
        "The quick brown fox jumps over the lazy dog.",
        "Machine learning is transforming the world of technology.",
        "Natural language processing enables computers to understand human language.",
        "Deep learning models require large amounts of data for training.",
        "Transformers have revolutionized the field of artificial intelligence.",
        "Language models can generate coherent and contextually relevant text.",
        "Training large models requires significant computational resources.",
        "Fine-tuning allows models to adapt to specific tasks and domains."
    ] * 100  # Repeat for more data
    
    # Save sample data
    sample_file = Path("data/samples/sample_text.txt")
    with open(sample_file, 'w') as f:
        for text in sample_texts:
            f.write(text + '\n')
    
    logger.info(f"Sample data saved to {sample_file}")
    logger.info(f"Sample dataset size: {len(sample_texts)} lines")


def check_gpu_availability(logger):
    """Check GPU availability and log information."""
    try:
        import torch
        
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            logger.info(f"GPU available: {gpu_name}")
            logger.info(f"GPU memory: {gpu_memory:.1f} GB")
        else:
            logger.warning("No GPU available - training will be slow")
            
    except ImportError:
        logger.error("PyTorch not available")


def main():
    """Main setup function."""
    logger = setup_logging()
    logger.info("Starting Colab setup...")
    
    try:
        install_packages(logger)
        setup_directories(logger)
        configure_environment(logger)
        download_sample_data(logger)
        check_gpu_availability(logger)
        
        logger.info("Colab setup completed successfully!")
        logger.info("You can now run the training notebooks.")
        
    except Exception as e:
        logger.error(f"Setup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
