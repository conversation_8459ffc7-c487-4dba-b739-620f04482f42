# Data Directory

This directory contains datasets and preprocessing scripts for training the LLM.

## Structure

```
data/
├── raw/                    # Raw downloaded datasets
├── processed/              # Preprocessed and tokenized data
├── scripts/               # Data processing scripts
└── samples/               # Small sample datasets for testing
```

## Supported Datasets

1. **OpenWebText** - Open source recreation of WebText
2. **BookCorpus** - Collection of books for language modeling
3. **Wikipedia** - Wikipedia articles dump
4. **Custom Text** - Your own text files

## Usage

1. Download datasets using `scripts/download_data.py`
2. Preprocess using `scripts/preprocess_data.py`
3. Tokenize using the BPE tokenizer

## File Formats

- Raw data: `.txt` files with one document per line
- Processed data: `.jsonl` files with metadata
- Tokenized data: `.bin` files for efficient loading
