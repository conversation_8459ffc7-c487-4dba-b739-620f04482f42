#!/usr/bin/env python3
"""
🚀 One-Click Google Colab Setup for LLM Training

This script sets up the complete LLM training environment in Google Colab.
Simply run this cell in Colab to get started immediately!

Usage in Colab:
    !wget https://raw.githubusercontent.com/your-repo/llm_from_scratch/main/colab_setup.py
    !python colab_setup.py
    
Or copy-paste this entire file into a Colab cell and run it.
"""

import os
import sys
import subprocess
import urllib.request
from pathlib import Path

def print_banner():
    """Print welcome banner."""
    banner = """
    🚀 LLM FROM SCRATCH - COLAB SETUP 🚀
    =====================================
    
    Setting up your complete LLM training environment...
    This will install dependencies and create the project structure.
    
    ⚡ Optimized for Google Colab Free Tier
    📚 Educational and Interactive
    🎯 Ready-to-run training pipeline
    
    """
    print(banner)

def check_environment():
    """Check if we're in Colab."""
    in_colab = 'google.colab' in sys.modules
    print(f"🌟 Environment: {'Google Colab' if in_colab else 'Local'}")
    print(f"🐍 Python: {sys.version}")
    
    if not in_colab:
        print("⚠️  This setup is optimized for Google Colab")
        print("   It should work locally but may need adjustments")
    
    return in_colab

def install_dependencies():
    """Install required packages."""
    print("\n📦 Installing dependencies...")
    
    packages = [
        "torch>=2.0.0",
        "numpy>=1.21.0", 
        "matplotlib>=3.4.0",
        "seaborn>=0.11.0",
        "tqdm>=4.62.0",
        "plotly>=5.0.0",
        "psutil>=5.8.0"
    ]
    
    for package in packages:
        try:
            print(f"  Installing {package}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-q", package
            ])
        except subprocess.CalledProcessError as e:
            print(f"  ⚠️ Warning: Failed to install {package}")
    
    print("✅ Dependencies installed!")

def create_project_structure():
    """Create the project directory structure."""
    print("\n📁 Creating project structure...")
    
    directories = [
        "llm_from_scratch",
        "llm_from_scratch/models",
        "llm_from_scratch/tokenizer", 
        "llm_from_scratch/training",
        "llm_from_scratch/utils",
        "config",
        "data",
        "data/samples",
        "notebooks",
        "checkpoints",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        
        # Create __init__.py files for Python packages
        if directory.startswith("llm_from_scratch"):
            init_file = Path(directory) / "__init__.py"
            if not init_file.exists():
                init_file.touch()
    
    # Create config __init__.py
    config_init = Path("config") / "__init__.py"
    if not config_init.exists():
        config_init.touch()
    
    print("✅ Project structure created!")

def create_sample_data():
    """Create sample training data."""
    print("\n📝 Creating sample data...")
    
    sample_texts = [
        "The quick brown fox jumps over the lazy dog.",
        "Machine learning is transforming artificial intelligence.",
        "Natural language processing enables computers to understand text.",
        "Deep learning models learn patterns from large datasets.",
        "Transformers revolutionized the field of language modeling.",
        "Attention mechanisms help models focus on relevant information.",
        "Neural networks consist of interconnected layers of neurons.",
        "Training requires optimization algorithms and loss functions.",
        "Gradient descent minimizes the loss function iteratively.",
        "Backpropagation computes gradients for parameter updates."
    ] * 50  # Repeat for more data
    
    # Save to file
    sample_file = Path("data/samples/training_data.txt")
    with open(sample_file, 'w') as f:
        for text in sample_texts:
            f.write(text + '\n')
    
    print(f"✅ Sample data created: {len(sample_texts)} samples")

def optimize_for_colab():
    """Apply Colab-specific optimizations."""
    print("\n⚙️ Applying Colab optimizations...")
    
    # Set environment variables
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'
    os.environ['TOKENIZERS_PARALLELISM'] = 'false'
    
    # Create cache directories
    cache_dirs = ['/content/hf_cache', '/content/transformers_cache']
    for cache_dir in cache_dirs:
        Path(cache_dir).mkdir(exist_ok=True)
    
    os.environ['HF_HOME'] = '/content/hf_cache'
    os.environ['TRANSFORMERS_CACHE'] = '/content/transformers_cache'
    
    print("✅ Colab optimizations applied!")

def create_quick_start_notebook():
    """Create a quick start notebook cell."""
    print("\n📓 Creating quick start code...")
    
    quick_start_code = '''
# 🚀 LLM Training Quick Start
# Copy this code to a new cell and run it to start training!

import torch
print(f"🔥 PyTorch: {torch.__version__}")
print(f"🎯 CUDA available: {torch.cuda.is_available()}")

if torch.cuda.is_available():
    print(f"🚀 GPU: {torch.cuda.get_device_name(0)}")

# Next steps:
print("\\n📚 Next steps:")
print("1. Open the training notebook: notebooks/02_complete_training_pipeline.ipynb")
print("2. Or run the training script directly")
print("3. Follow the step-by-step instructions")

print("\\n🎉 Setup complete! Ready to train your LLM!")
'''
    
    # Save quick start code
    with open("quick_start.py", 'w') as f:
        f.write(quick_start_code)
    
    print("✅ Quick start code created!")

def display_next_steps():
    """Display next steps for the user."""
    next_steps = """
    
    🎉 SETUP COMPLETE! 🎉
    ====================
    
    Your LLM training environment is ready!
    
    📚 Next Steps:
    
    1. 📓 RECOMMENDED: Open the complete training notebook
       • File: notebooks/02_complete_training_pipeline.ipynb
       • This contains the full interactive training pipeline
       • Step-by-step instructions with visualizations
    
    2. 🚀 QUICK START: Run the training script
       • Execute: !python quick_start.py
       • Then follow the printed instructions
    
    3. 🔧 CUSTOMIZE: Modify configurations
       • Edit model parameters in config/
       • Adjust training settings for your needs
    
    4. 📊 MONITOR: Watch your model train
       • Real-time loss curves
       • Memory usage monitoring  
       • Text generation samples
    
    💡 Tips:
    • Start with the TINY model configuration for Colab
    • Monitor memory usage to avoid crashes
    • Experiment with different hyperparameters
    • Save checkpoints regularly
    
    🎯 Ready to build your first LLM from scratch!
    
    """
    print(next_steps)

def main():
    """Main setup function."""
    try:
        print_banner()
        
        # Check environment
        in_colab = check_environment()
        
        # Install dependencies
        install_dependencies()
        
        # Create project structure
        create_project_structure()
        
        # Create sample data
        create_sample_data()
        
        # Apply optimizations
        optimize_for_colab()
        
        # Create quick start
        create_quick_start_notebook()
        
        # Show next steps
        display_next_steps()
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        print("Please check the error and try again.")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("✅ Setup completed successfully!")
    else:
        print("❌ Setup failed. Please check the errors above.")
