# Check if we're in Colab and setup accordingly
import sys
import os
from pathlib import Path

# Detect environment
IN_COLAB = 'google.colab' in sys.modules
print(f"🌟 Running in Google Colab: {IN_COLAB}")
print(f"🐍 Python version: {sys.version}")

if IN_COLAB:
    print("📦 Installing additional packages for Colab...")
    !pip install -q tokenizers datasets tqdm matplotlib seaborn plotly
    
    # Clone or setup project structure if needed
    if not Path('llm_from_scratch').exists():
        print("📁 Setting up project structure...")
        !mkdir -p llm_from_scratch/{models,tokenizer,training,utils}
        !mkdir -p {config,data,checkpoints,logs}
        !touch llm_from_scratch/__init__.py
        !touch llm_from_scratch/{models,tokenizer,training,utils}/__init__.py
        !touch config/__init__.py
        
print("✅ Environment setup complete!")

# Import all necessary libraries
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px

from tqdm.auto import tqdm
import json
import re
import time
import logging
from collections import Counter, defaultdict
from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

# Set up plotting
plt.style.use('default')
sns.set_palette("husl")
%matplotlib inline

print("📚 All libraries imported successfully!")
print(f"🔥 PyTorch version: {torch.__version__}")
print(f"🎯 CUDA available: {torch.cuda.is_available()}")

if torch.cuda.is_available():
    print(f"🚀 GPU: {torch.cuda.get_device_name(0)}")
    print(f"💾 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

# Memory optimization for Colab
def optimize_for_colab():
    """Apply Colab-specific optimizations."""
    # Set CUDA memory allocation strategy
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'
    
    # Clear cache
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.reset_peak_memory_stats()
    
    # Enable memory efficient attention if available
    try:
        torch.backends.cuda.enable_flash_sdp(True)
        print("⚡ Flash Attention enabled")
    except:
        print("⚠️ Flash Attention not available")
    
    print("🔧 Colab optimizations applied!")

optimize_for_colab()

# Memory monitoring utilities
def get_memory_usage():
    """Get current memory usage statistics."""
    import psutil
    
    memory_info = {}
    
    # System memory
    system_memory = psutil.virtual_memory()
    memory_info['system'] = {
        'total_gb': system_memory.total / (1024**3),
        'available_gb': system_memory.available / (1024**3),
        'used_gb': system_memory.used / (1024**3),
        'percent': system_memory.percent
    }
    
    # GPU memory (if available)
    if torch.cuda.is_available():
        memory_info['gpu'] = {
            'allocated_gb': torch.cuda.memory_allocated() / (1024**3),
            'reserved_gb': torch.cuda.memory_reserved() / (1024**3),
            'total_gb': torch.cuda.get_device_properties(0).total_memory / (1024**3)
        }
    
    return memory_info

def display_memory_usage():
    """Display current memory usage with visual bars."""
    memory_info = get_memory_usage()
    
    print("💾 Memory Usage:")
    
    # System memory
    sys_mem = memory_info['system']
    sys_bar = '█' * int(sys_mem['percent'] / 5) + '░' * (20 - int(sys_mem['percent'] / 5))
    print(f"  System: [{sys_bar}] {sys_mem['percent']:.1f}% ({sys_mem['used_gb']:.1f}GB / {sys_mem['total_gb']:.1f}GB)")
    
    # GPU memory
    if 'gpu' in memory_info:
        gpu_mem = memory_info['gpu']
        gpu_percent = (gpu_mem['allocated_gb'] / gpu_mem['total_gb']) * 100
        gpu_bar = '█' * int(gpu_percent / 5) + '░' * (20 - int(gpu_percent / 5))
        print(f"  GPU:    [{gpu_bar}] {gpu_percent:.1f}% ({gpu_mem['allocated_gb']:.1f}GB / {gpu_mem['total_gb']:.1f}GB)")

display_memory_usage()

# Model configuration optimized for Colab
@dataclass
class ModelConfig:
    """Configuration for our tiny transformer model."""
    
    # Model architecture (TINY for Colab)
    vocab_size: int = 1000  # Small vocab for demo
    max_seq_length: int = 128  # Short sequences
    d_model: int = 256  # Hidden dimension
    n_heads: int = 4  # Number of attention heads
    n_layers: int = 4  # Number of transformer blocks
    d_ff: int = 1024  # Feed-forward dimension
    dropout: float = 0.1
    
    # Training parameters
    batch_size: int = 16  # Small batch for Colab
    learning_rate: float = 3e-4
    weight_decay: float = 0.01
    warmup_steps: int = 100
    max_steps: int = 1000  # Quick demo
    eval_interval: int = 100
    
    # Optimization
    gradient_accumulation_steps: int = 2
    max_grad_norm: float = 1.0
    use_mixed_precision: bool = True

# Create configuration
config = ModelConfig()

print("⚙️ Model Configuration:")
print(f"  Model size: {config.d_model} hidden dimensions")
print(f"  Layers: {config.n_layers}")
print(f"  Attention heads: {config.n_heads}")
print(f"  Vocabulary size: {config.vocab_size}")
print(f"  Max sequence length: {config.max_seq_length}")
print(f"  Batch size: {config.batch_size}")
print(f"  Training steps: {config.max_steps}")

# Estimate model size
model_params = (
    config.vocab_size * config.d_model +  # Token embeddings
    config.max_seq_length * config.d_model +  # Position embeddings
    config.n_layers * (
        4 * config.d_model * config.d_model +  # Attention weights
        2 * config.d_model * config.d_ff  # FFN weights
    )
)
model_size_mb = model_params * 4 / (1024 * 1024)  # float32
print(f"  Estimated model size: ~{model_size_mb:.1f} MB")

# Generate sample training data
def generate_sample_data(num_samples=1000):
    """Generate sample text data for training."""
    
    # Templates for different types of sentences
    templates = [
        "The {adjective} {noun} {verb} {adverb} in the {location}.",
        "{name} is a {profession} who {action} every {time}.",
        "In {year}, {event} happened in {place}.",
        "The {color} {object} was {adjective} and {adjective2}.",
        "Machine learning {verb} {noun} through {method}.",
        "Natural language processing {action} {object} using {technique}.",
        "Deep learning models {verb} {data_type} to {goal}.",
        "Artificial intelligence {action} {domain} by {approach}."
    ]
    
    # Word lists
    words = {
        'adjective': ['quick', 'smart', 'large', 'small', 'bright', 'dark', 'fast', 'slow', 'new', 'old'],
        'adjective2': ['beautiful', 'useful', 'important', 'interesting', 'complex', 'simple', 'modern', 'ancient'],
        'noun': ['cat', 'dog', 'house', 'car', 'book', 'computer', 'phone', 'tree', 'flower', 'mountain'],
        'verb': ['runs', 'walks', 'flies', 'swims', 'jumps', 'learns', 'teaches', 'creates', 'builds', 'discovers'],
        'adverb': ['quickly', 'slowly', 'carefully', 'happily', 'quietly', 'loudly', 'smoothly', 'efficiently'],
        'location': ['park', 'forest', 'city', 'ocean', 'mountain', 'desert', 'garden', 'library', 'school'],
        'name': ['Alice', 'Bob', 'Charlie', 'Diana', 'Eve', 'Frank', 'Grace', 'Henry', 'Iris', 'Jack'],
        'profession': ['teacher', 'doctor', 'engineer', 'artist', 'scientist', 'writer', 'musician', 'chef'],
        'action': ['studies', 'works', 'practices', 'researches', 'develops', 'analyzes', 'creates', 'explores'],
        'time': ['day', 'week', 'month', 'year', 'morning', 'evening', 'weekend', 'season'],
        'year': ['2020', '2021', '2022', '2023', '2024', '1990', '2000', '2010'],
        'event': ['a discovery', 'an invention', 'a breakthrough', 'a revolution', 'an innovation'],
        'place': ['Silicon Valley', 'New York', 'London', 'Tokyo', 'Paris', 'Berlin', 'Boston'],
        'color': ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'black', 'white', 'silver'],
        'object': ['robot', 'algorithm', 'system', 'network', 'database', 'application', 'model', 'framework'],
        'method': ['algorithms', 'neural networks', 'deep learning', 'machine learning', 'data analysis'],
        'technique': ['transformers', 'attention mechanisms', 'neural networks', 'statistical methods'],
        'data_type': ['text', 'images', 'audio', 'video', 'data', 'information', 'patterns'],
        'goal': ['predict', 'classify', 'generate', 'understand', 'analyze', 'process', 'learn'],
        'domain': ['healthcare', 'finance', 'education', 'transportation', 'entertainment', 'research'],
        'approach': ['learning patterns', 'analyzing data', 'processing information', 'understanding context']
    }
    
    texts = []
    for _ in range(num_samples):
        template = np.random.choice(templates)
        
        # Fill in the template
        text = template
        for key, word_list in words.items():
            if '{' + key + '}' in text:
                text = text.replace('{' + key + '}', np.random.choice(word_list))
        
        texts.append(text)
    
    return texts

# Generate training data
print("🎲 Generating sample training data...")
train_texts = generate_sample_data(800)
val_texts = generate_sample_data(200)

print(f"📊 Generated {len(train_texts)} training samples")
print(f"📊 Generated {len(val_texts)} validation samples")

# Show some examples
print("\n📝 Sample texts:")
for i, text in enumerate(train_texts[:5]):
    print(f"  {i+1}. {text}")

# Analyze the generated data
all_text = ' '.join(train_texts + val_texts)
words = all_text.lower().split()
chars = list(all_text)

print("📈 Data Statistics:")
print(f"  Total characters: {len(chars):,}")
print(f"  Unique characters: {len(set(chars))}")
print(f"  Total words: {len(words):,}")
print(f"  Unique words: {len(set(words))}")
print(f"  Average text length: {np.mean([len(text) for text in train_texts]):.1f} characters")

# Visualize word frequency
word_counts = Counter(words)
top_words = word_counts.most_common(20)

fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))

# Word frequency
words_df = pd.DataFrame(top_words, columns=['word', 'count'])
ax1.bar(range(len(words_df)), words_df['count'])
ax1.set_xticks(range(len(words_df)))
ax1.set_xticklabels(words_df['word'], rotation=45)
ax1.set_title('Top 20 Most Frequent Words')
ax1.set_ylabel('Frequency')

# Text length distribution
text_lengths = [len(text) for text in train_texts]
ax2.hist(text_lengths, bins=20, alpha=0.7, edgecolor='black')
ax2.set_title('Distribution of Text Lengths')
ax2.set_xlabel('Text Length (characters)')
ax2.set_ylabel('Frequency')

plt.tight_layout()
plt.show()

print(f"\n🔤 Character set: {sorted(set(chars))}")

# Simple character-level tokenizer for demo
class SimpleTokenizer:
    """Simple character-level tokenizer for demonstration."""
    
    def __init__(self):
        self.char_to_id = {}
        self.id_to_char = {}
        self.vocab_size = 0
        
        # Special tokens
        self.pad_token = '<PAD>'
        self.unk_token = '<UNK>'
        self.bos_token = '<BOS>'
        self.eos_token = '<EOS>'
        
    def train(self, texts):
        """Train tokenizer on texts."""
        print("🔤 Training tokenizer...")
        
        # Collect all characters
        all_chars = set()
        for text in texts:
            all_chars.update(text.lower())
        
        # Build vocabulary
        vocab = [self.pad_token, self.unk_token, self.bos_token, self.eos_token]
        vocab.extend(sorted(all_chars))
        
        self.char_to_id = {char: i for i, char in enumerate(vocab)}
        self.id_to_char = {i: char for i, char in enumerate(vocab)}
        self.vocab_size = len(vocab)
        
        print(f"✅ Tokenizer trained with vocabulary size: {self.vocab_size}")
        print(f"📝 Special tokens: {vocab[:4]}")
        
    def encode(self, text, max_length=None):
        """Encode text to token IDs."""
        text = text.lower()
        tokens = [self.char_to_id[self.bos_token]]
        
        for char in text:
            if char in self.char_to_id:
                tokens.append(self.char_to_id[char])
            else:
                tokens.append(self.char_to_id[self.unk_token])
        
        tokens.append(self.char_to_id[self.eos_token])
        
        # Truncate or pad
        if max_length:
            if len(tokens) > max_length:
                tokens = tokens[:max_length]
            else:
                tokens.extend([self.char_to_id[self.pad_token]] * (max_length - len(tokens)))
        
        return tokens
    
    def decode(self, token_ids):
        """Decode token IDs to text."""
        chars = []
        for token_id in token_ids:
            if token_id in self.id_to_char:
                char = self.id_to_char[token_id]
                if char not in [self.pad_token, self.bos_token, self.eos_token]:
                    chars.append(char)
        return ''.join(chars)

# Train tokenizer
tokenizer = SimpleTokenizer()
tokenizer.train(train_texts)

# Update config with actual vocab size
config.vocab_size = tokenizer.vocab_size
print(f"📊 Updated config vocab_size to: {config.vocab_size}")

# Test tokenizer
test_text = "The quick brown fox jumps."
encoded = tokenizer.encode(test_text, max_length=config.max_seq_length)
decoded = tokenizer.decode(encoded)

print(f"\n🧪 Tokenizer Test:")
print(f"  Original: {test_text}")
print(f"  Encoded:  {encoded[:20]}...")
print(f"  Decoded:  {decoded}")

# Multi-Head Attention implementation
class MultiHeadAttention(nn.Module):
    """Multi-head self-attention mechanism."""
    
    def __init__(self, d_model, n_heads, dropout=0.1):
        super().__init__()
        assert d_model % n_heads == 0
        
        self.d_model = d_model
        self.n_heads = n_heads
        self.d_k = d_model // n_heads
        
        self.w_q = nn.Linear(d_model, d_model, bias=False)
        self.w_k = nn.Linear(d_model, d_model, bias=False)
        self.w_v = nn.Linear(d_model, d_model, bias=False)
        self.w_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, mask=None):
        batch_size, seq_len, d_model = x.size()
        
        # Linear projections
        Q = self.w_q(x).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        K = self.w_k(x).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        V = self.w_v(x).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        
        # Scaled dot-product attention
        scores = torch.matmul(Q, K.transpose(-2, -1)) / np.sqrt(self.d_k)
        
        # Apply causal mask
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # Apply attention to values
        context = torch.matmul(attention_weights, V)
        
        # Concatenate heads
        context = context.transpose(1, 2).contiguous().view(
            batch_size, seq_len, d_model
        )
        
        # Final linear projection
        output = self.w_o(context)
        
        return output, attention_weights

print("✅ Multi-Head Attention implemented")

# Feed-Forward Network
class FeedForward(nn.Module):
    """Position-wise feed-forward network."""
    
    def __init__(self, d_model, d_ff, dropout=0.1):
        super().__init__()
        self.linear1 = nn.Linear(d_model, d_ff)
        self.linear2 = nn.Linear(d_ff, d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        return self.linear2(self.dropout(F.gelu(self.linear1(x))))

# Layer Normalization
class LayerNorm(nn.Module):
    """Layer normalization."""
    
    def __init__(self, d_model, eps=1e-6):
        super().__init__()
        self.gamma = nn.Parameter(torch.ones(d_model))
        self.beta = nn.Parameter(torch.zeros(d_model))
        self.eps = eps
        
    def forward(self, x):
        mean = x.mean(-1, keepdim=True)
        std = x.std(-1, keepdim=True)
        return self.gamma * (x - mean) / (std + self.eps) + self.beta

print("✅ Feed-Forward and LayerNorm implemented")

# Transformer Block
class TransformerBlock(nn.Module):
    """Single transformer block with attention and feed-forward."""
    
    def __init__(self, d_model, n_heads, d_ff, dropout=0.1):
        super().__init__()
        self.attention = MultiHeadAttention(d_model, n_heads, dropout)
        self.feed_forward = FeedForward(d_model, d_ff, dropout)
        self.norm1 = LayerNorm(d_model)
        self.norm2 = LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, mask=None):
        # Self-attention with residual connection
        attn_output, attn_weights = self.attention(self.norm1(x), mask)
        x = x + self.dropout(attn_output)
        
        # Feed-forward with residual connection
        ff_output = self.feed_forward(self.norm2(x))
        x = x + self.dropout(ff_output)
        
        return x, attn_weights

print("✅ Transformer Block implemented")

# Complete Transformer Model
class TransformerLM(nn.Module):
    """Complete transformer language model."""
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        
        # Embeddings
        self.token_embedding = nn.Embedding(config.vocab_size, config.d_model)
        self.position_embedding = nn.Embedding(config.max_seq_length, config.d_model)
        
        # Transformer blocks
        self.blocks = nn.ModuleList([
            TransformerBlock(config.d_model, config.n_heads, config.d_ff, config.dropout)
            for _ in range(config.n_layers)
        ])
        
        # Output
        self.norm = LayerNorm(config.d_model)
        self.lm_head = nn.Linear(config.d_model, config.vocab_size, bias=False)
        
        # Initialize weights
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        """Initialize weights."""
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
    
    def forward(self, input_ids, targets=None):
        batch_size, seq_len = input_ids.size()
        
        # Create causal mask
        mask = torch.tril(torch.ones(seq_len, seq_len)).unsqueeze(0).unsqueeze(0)
        mask = mask.to(input_ids.device)
        
        # Embeddings
        positions = torch.arange(0, seq_len, device=input_ids.device).unsqueeze(0)
        token_emb = self.token_embedding(input_ids)
        pos_emb = self.position_embedding(positions)
        x = token_emb + pos_emb
        
        # Apply transformer blocks
        attention_weights = []
        for block in self.blocks:
            x, attn_weights = block(x, mask)
            attention_weights.append(attn_weights)
        
        # Final normalization and projection
        x = self.norm(x)
        logits = self.lm_head(x)
        
        loss = None
        if targets is not None:
            loss = F.cross_entropy(
                logits.view(-1, logits.size(-1)),
                targets.view(-1),
                ignore_index=tokenizer.char_to_id[tokenizer.pad_token]
            )
        
        return {
            'logits': logits,
            'loss': loss,
            'attention_weights': attention_weights
        }
    
    def generate(self, input_ids, max_length=50, temperature=1.0):
        """Generate text using the model."""
        self.eval()
        
        with torch.no_grad():
            for _ in range(max_length):
                # Forward pass
                outputs = self(input_ids)
                logits = outputs['logits']
                
                # Get next token probabilities
                next_token_logits = logits[:, -1, :] / temperature
                probs = F.softmax(next_token_logits, dim=-1)
                
                # Sample next token
                next_token = torch.multinomial(probs, num_samples=1)
                
                # Append to sequence
                input_ids = torch.cat([input_ids, next_token], dim=1)
                
                # Stop if we hit EOS token
                if next_token.item() == tokenizer.char_to_id[tokenizer.eos_token]:
                    break
        
        return input_ids

print("✅ Complete Transformer Language Model implemented")

# Dataset class
class TextDataset(Dataset):
    """Dataset for text data."""
    
    def __init__(self, texts, tokenizer, max_length):
        self.texts = texts
        self.tokenizer = tokenizer
        self.max_length = max_length
        
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = self.texts[idx]
        tokens = self.tokenizer.encode(text, max_length=self.max_length)
        
        # Input and target (shifted by one)
        input_ids = torch.tensor(tokens[:-1], dtype=torch.long)
        targets = torch.tensor(tokens[1:], dtype=torch.long)
        
        return input_ids, targets

# Create datasets and data loaders
train_dataset = TextDataset(train_texts, tokenizer, config.max_seq_length)
val_dataset = TextDataset(val_texts, tokenizer, config.max_seq_length)

train_loader = DataLoader(
    train_dataset, 
    batch_size=config.batch_size, 
    shuffle=True,
    num_workers=0  # Set to 0 for Colab compatibility
)

val_loader = DataLoader(
    val_dataset, 
    batch_size=config.batch_size, 
    shuffle=False,
    num_workers=0
)

print(f"📊 Training batches: {len(train_loader)}")
print(f"📊 Validation batches: {len(val_loader)}")

# Test data loading
sample_batch = next(iter(train_loader))
input_ids, targets = sample_batch
print(f"\n🔍 Sample batch shape:")
print(f"  Input IDs: {input_ids.shape}")
print(f"  Targets: {targets.shape}")
print(f"  Sample text: {tokenizer.decode(input_ids[0].tolist())}")

# Initialize model and optimizer
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"🎯 Using device: {device}")

# Create model
model = TransformerLM(config).to(device)

# Count parameters
total_params = sum(p.numel() for p in model.parameters())
trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

print(f"\n🧠 Model Information:")
print(f"  Total parameters: {total_params:,}")
print(f"  Trainable parameters: {trainable_params:,}")
print(f"  Model size: {total_params * 4 / 1024**2:.2f} MB")

# Optimizer and scheduler
optimizer = optim.AdamW(
    model.parameters(),
    lr=config.learning_rate,
    weight_decay=config.weight_decay
)

# Learning rate scheduler
def get_lr(step):
    if step < config.warmup_steps:
        return step / config.warmup_steps
    else:
        return 0.5 * (1 + np.cos(np.pi * (step - config.warmup_steps) / (config.max_steps - config.warmup_steps)))

scheduler = optim.lr_scheduler.LambdaLR(optimizer, get_lr)

# Mixed precision scaler
scaler = torch.cuda.amp.GradScaler() if config.use_mixed_precision and torch.cuda.is_available() else None

print(f"⚙️ Optimizer: AdamW (lr={config.learning_rate})")
print(f"📈 Scheduler: Cosine with warmup ({config.warmup_steps} steps)")
print(f"🔥 Mixed precision: {config.use_mixed_precision and torch.cuda.is_available()}")

# Test forward pass
model.eval()
with torch.no_grad():
    test_input = input_ids[:2].to(device)
    test_targets = targets[:2].to(device)
    outputs = model(test_input, test_targets)
    print(f"\n🧪 Test forward pass:")
    print(f"  Input shape: {test_input.shape}")
    print(f"  Output logits shape: {outputs['logits'].shape}")
    print(f"  Loss: {outputs['loss']:.4f}")

display_memory_usage()

# Training metrics tracking
class MetricsTracker:
    """Track and visualize training metrics."""
    
    def __init__(self):
        self.metrics = {
            'step': [],
            'train_loss': [],
            'val_loss': [],
            'learning_rate': [],
            'grad_norm': [],
            'perplexity': []
        }
    
    def update(self, step, train_loss=None, val_loss=None, lr=None, grad_norm=None):
        """Update metrics."""
        self.metrics['step'].append(step)
        self.metrics['train_loss'].append(train_loss)
        self.metrics['val_loss'].append(val_loss)
        self.metrics['learning_rate'].append(lr)
        self.metrics['grad_norm'].append(grad_norm)
        
        if train_loss is not None:
            self.metrics['perplexity'].append(np.exp(train_loss))
        else:
            self.metrics['perplexity'].append(None)
    
    def plot_metrics(self):
        """Plot training metrics."""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Loss curves
        ax = axes[0, 0]
        if any(x is not None for x in self.metrics['train_loss']):
            train_steps = [s for s, l in zip(self.metrics['step'], self.metrics['train_loss']) if l is not None]
            train_losses = [l for l in self.metrics['train_loss'] if l is not None]
            ax.plot(train_steps, train_losses, 'b-', label='Train Loss', alpha=0.7)
        
        if any(x is not None for x in self.metrics['val_loss']):
            val_steps = [s for s, l in zip(self.metrics['step'], self.metrics['val_loss']) if l is not None]
            val_losses = [l for l in self.metrics['val_loss'] if l is not None]
            ax.plot(val_steps, val_losses, 'r-', label='Val Loss', alpha=0.7)
        
        ax.set_xlabel('Step')
        ax.set_ylabel('Loss')
        ax.set_title('Training and Validation Loss')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # Learning rate
        ax = axes[0, 1]
        if any(x is not None for x in self.metrics['learning_rate']):
            lr_steps = [s for s, lr in zip(self.metrics['step'], self.metrics['learning_rate']) if lr is not None]
            lrs = [lr for lr in self.metrics['learning_rate'] if lr is not None]
            ax.plot(lr_steps, lrs, 'g-', alpha=0.7)
        ax.set_xlabel('Step')
        ax.set_ylabel('Learning Rate')
        ax.set_title('Learning Rate Schedule')
        ax.grid(True, alpha=0.3)
        
        # Gradient norm
        ax = axes[1, 0]
        if any(x is not None for x in self.metrics['grad_norm']):
            grad_steps = [s for s, g in zip(self.metrics['step'], self.metrics['grad_norm']) if g is not None]
            grad_norms = [g for g in self.metrics['grad_norm'] if g is not None]
            ax.plot(grad_steps, grad_norms, 'orange', alpha=0.7)
        ax.set_xlabel('Step')
        ax.set_ylabel('Gradient Norm')
        ax.set_title('Gradient Norm')
        ax.grid(True, alpha=0.3)
        
        # Perplexity
        ax = axes[1, 1]
        if any(x is not None for x in self.metrics['perplexity']):
            perp_steps = [s for s, p in zip(self.metrics['step'], self.metrics['perplexity']) if p is not None]
            perplexities = [p for p in self.metrics['perplexity'] if p is not None]
            ax.plot(perp_steps, perplexities, 'purple', alpha=0.7)
        ax.set_xlabel('Step')
        ax.set_ylabel('Perplexity')
        ax.set_title('Perplexity')
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()

# Initialize metrics tracker
metrics_tracker = MetricsTracker()
print("📊 Metrics tracker initialized")

# Evaluation function
def evaluate_model(model, val_loader, device):
    """Evaluate model on validation set."""
    model.eval()
    total_loss = 0
    num_batches = 0
    
    with torch.no_grad():
        for input_ids, targets in val_loader:
            input_ids = input_ids.to(device)
            targets = targets.to(device)
            
            outputs = model(input_ids, targets)
            total_loss += outputs['loss'].item()
            num_batches += 1
    
    avg_loss = total_loss / num_batches
    perplexity = np.exp(avg_loss)
    
    return avg_loss, perplexity

print("✅ Evaluation function ready")

# Main training loop
def train_model():
    """Main training loop with real-time visualization."""
    print("🚀 Starting training...")
    
    model.train()
    step = 0
    running_loss = 0
    
    # Progress bar
    pbar = tqdm(total=config.max_steps, desc="Training")
    
    while step < config.max_steps:
        for batch_idx, (input_ids, targets) in enumerate(train_loader):
            if step >= config.max_steps:
                break
            
            # Move to device
            input_ids = input_ids.to(device)
            targets = targets.to(device)
            
            # Forward pass
            if config.use_mixed_precision and scaler is not None:
                with torch.cuda.amp.autocast():
                    outputs = model(input_ids, targets)
                    loss = outputs['loss'] / config.gradient_accumulation_steps
            else:
                outputs = model(input_ids, targets)
                loss = outputs['loss'] / config.gradient_accumulation_steps
            
            # Backward pass
            if config.use_mixed_precision and scaler is not None:
                scaler.scale(loss).backward()
            else:
                loss.backward()
            
            running_loss += loss.item() * config.gradient_accumulation_steps
            
            # Update weights
            if (step + 1) % config.gradient_accumulation_steps == 0:
                # Gradient clipping
                if config.use_mixed_precision and scaler is not None:
                    scaler.unscale_(optimizer)
                    grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), config.max_grad_norm)
                    scaler.step(optimizer)
                    scaler.update()
                else:
                    grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), config.max_grad_norm)
                    optimizer.step()
                
                scheduler.step()
                optimizer.zero_grad()
                
                # Update metrics
                current_lr = scheduler.get_last_lr()[0]
                metrics_tracker.update(
                    step=step,
                    train_loss=running_loss,
                    lr=current_lr,
                    grad_norm=grad_norm.item()
                )
                
                # Update progress bar
                pbar.set_postfix({
                    'loss': f'{running_loss:.4f}',
                    'lr': f'{current_lr:.2e}',
                    'grad_norm': f'{grad_norm:.4f}'
                })
                pbar.update(1)
                
                running_loss = 0
            
            # Evaluation
            if step % config.eval_interval == 0 and step > 0:
                val_loss, perplexity = evaluate_model(model, val_loader, device)
                
                print(f"\n📊 Step {step} Evaluation:")
                print(f"  Validation Loss: {val_loss:.4f}")
                print(f"  Perplexity: {perplexity:.2f}")
                
                # Update metrics with validation loss
                metrics_tracker.metrics['val_loss'][-1] = val_loss
                
                # Plot metrics
                metrics_tracker.plot_metrics()
                
                # Generate sample text
                generate_sample_text(model, tokenizer, device)
                
                # Memory usage
                display_memory_usage()
                
                model.train()
            
            step += 1
    
    pbar.close()
    print("\n🎉 Training completed!")

print("✅ Training function ready")

# Text generation function
def generate_sample_text(model, tokenizer, device, prompt="The", max_length=50):
    """Generate sample text from the model."""
    model.eval()
    
    print(f"\n🎭 Generating text with prompt: '{prompt}'")
    
    # Encode prompt
    input_ids = torch.tensor(
        tokenizer.encode(prompt, max_length=None), 
        dtype=torch.long
    ).unsqueeze(0).to(device)
    
    # Generate text
    with torch.no_grad():
        generated = model.generate(input_ids, max_length=max_length, temperature=0.8)
    
    # Decode generated text
    generated_text = tokenizer.decode(generated[0].cpu().tolist())
    
    print(f"  Generated: {generated_text}")
    
    return generated_text

print("✅ Text generation function ready")

# Final setup check
print("🔍 Final setup check:")
print(f"  Device: {device}")
print(f"  Model parameters: {total_params:,}")
print(f"  Training steps: {config.max_steps}")
print(f"  Batch size: {config.batch_size}")
print(f"  Learning rate: {config.learning_rate}")
print(f"  Mixed precision: {config.use_mixed_precision and torch.cuda.is_available()}")

# Memory check
display_memory_usage()

# Generate initial sample (before training)
print("\n🎭 Sample generation before training:")
generate_sample_text(model, tokenizer, device, "The quick")
generate_sample_text(model, tokenizer, device, "Machine learning")

print("\n" + "="*50)
print("🚀 READY TO START TRAINING!")
print("Run the next cell to begin training your LLM!")
print("="*50)

# 🚀 START TRAINING!
# Uncomment the line below to start training
train_model()

# Final evaluation
print("\n📊 Final Evaluation:")
final_val_loss, final_perplexity = evaluate_model(model, val_loader, device)
print(f"  Final Validation Loss: {final_val_loss:.4f}")
print(f"  Final Perplexity: {final_perplexity:.2f}")

# Final metrics plot
metrics_tracker.plot_metrics()

# Generate final samples
print("\n🎭 Final text generation samples:")
prompts = ["The", "Machine learning", "Natural language", "Deep learning"]
for prompt in prompts:
    generate_sample_text(model, tokenizer, device, prompt, max_length=30)

print("\n🎉 Training completed successfully!")
print("Your LLM is now trained and ready to generate text!")