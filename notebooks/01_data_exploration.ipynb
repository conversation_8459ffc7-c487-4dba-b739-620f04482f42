{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Data Exploration and Preprocessing\n", "\n", "This notebook demonstrates how to explore and preprocess text data for training our LLM.\n", "\n", "## Objectives\n", "1. Load and explore sample datasets\n", "2. Understand data characteristics\n", "3. Implement preprocessing pipeline\n", "4. Prepare data for tokenization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup\n", "import sys\n", "sys.path.append('..')\n", "\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from collections import Counter\n", "import re\n", "\n", "# Our modules\n", "from llm_from_scratch.utils import download_dataset, prepare_text_data\n", "from config import DEFAULT_CONFIG"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading and Basic Statistics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load sample data\n", "# We'll start with a small dataset for demonstration\n", "sample_texts = [\n", "    \"The quick brown fox jumps over the lazy dog.\",\n", "    \"Machine learning is a subset of artificial intelligence.\",\n", "    \"Natural language processing enables computers to understand human language.\"\n", "]\n", "\n", "print(f\"Number of sample texts: {len(sample_texts)}\")\n", "print(f\"Average text length: {np.mean([len(text) for text in sample_texts]):.1f} characters\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Text Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze character and word distributions\n", "all_text = ' '.join(sample_texts)\n", "words = all_text.lower().split()\n", "chars = list(all_text)\n", "\n", "print(f\"Total characters: {len(chars)}\")\n", "print(f\"Unique characters: {len(set(chars))}\")\n", "print(f\"Total words: {len(words)}\")\n", "print(f\"Unique words: {len(set(words))}\")\n", "\n", "# Most common words\n", "word_counts = Counter(words)\n", "print(\"\\nMost common words:\")\n", "for word, count in word_counts.most_common(10):\n", "    print(f\"  {word}: {count}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Preprocessing Pipeline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def preprocess_text(text: str) -> str:\n", "    \"\"\"\n", "    Basic text preprocessing.\n", "    \n", "    Args:\n", "        text: Raw text string\n", "        \n", "    Returns:\n", "        Preprocessed text\n", "    \"\"\"\n", "    # Remove extra whitespace\n", "    text = re.sub(r'\\s+', ' ', text)\n", "    \n", "    # Strip leading/trailing whitespace\n", "    text = text.strip()\n", "    \n", "    return text\n", "\n", "# Apply preprocessing\n", "processed_texts = [preprocess_text(text) for text in sample_texts]\n", "\n", "print(\"Original vs Processed:\")\n", "for orig, proc in zip(sample_texts[:2], processed_texts[:2]):\n", "    print(f\"Original:  {repr(orig)}\")\n", "    print(f\"Processed: {repr(proc)}\")\n", "    print()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}