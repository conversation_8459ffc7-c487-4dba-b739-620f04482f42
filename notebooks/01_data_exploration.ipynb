# Check if we're in Colab and setup accordingly
import sys
import os
from pathlib import Path

# Detect environment
IN_COLAB = 'google.colab' in sys.modules
print(f"🌟 Running in Google Colab: {IN_COLAB}")
print(f"🐍 Python version: {sys.version}")

if IN_COLAB:
    print("📦 Installing additional packages for Colab...")
    !pip install -q tokenizers datasets tqdm matplotlib seaborn plotly
    
    # Clone or setup project structure if needed
    if not Path('llm_from_scratch').exists():
        print("📁 Setting up project structure...")
        !mkdir -p llm_from_scratch/{models,tokenizer,training,utils}
        !mkdir -p {config,data,checkpoints,logs}
        !touch llm_from_scratch/__init__.py
        !touch llm_from_scratch/{models,tokenizer,training,utils}/__init__.py
        !touch config/__init__.py
        
print("✅ Environment setup complete!")

# Import all necessary libraries
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px

from tqdm.auto import tqdm
import json
import re
import time
import logging
from collections import Counter, defaultdict
from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')

# Set up plotting
plt.style.use('default')
sns.set_palette("husl")
%matplotlib inline

print("📚 All libraries imported successfully!")
print(f"🔥 PyTorch version: {torch.__version__}")
print(f"🎯 CUDA available: {torch.cuda.is_available()}")

if torch.cuda.is_available():
    print(f"🚀 GPU: {torch.cuda.get_device_name(0)}")
    print(f"💾 GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")