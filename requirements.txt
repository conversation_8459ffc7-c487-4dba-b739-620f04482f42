# Core dependencies
torch>=2.0.0
numpy>=1.21.0
pandas>=1.3.0
matplotlib>=3.4.0
seaborn>=0.11.0
tqdm>=4.62.0

# Data processing
datasets>=2.0.0
transformers>=4.20.0
tokenizers>=0.13.0
requests>=2.25.0

# Training and optimization
accelerate>=0.20.0
wandb>=0.15.0
tensorboard>=2.8.0

# Utilities
psutil>=5.8.0
jsonlines>=3.0.0
regex>=2022.0.0

# Development and testing
pytest>=7.0.0
black>=22.0.0
flake8>=4.0.0
mypy>=0.950

# Jupyter notebooks
jupyter>=1.0.0
ipywidgets>=7.6.0
plotly>=5.0.0

# Optional: For advanced optimizations
# triton>=2.0.0  # Uncomment if using custom kernels
# flash-attn>=2.0.0  # Uncomment for flash attention
