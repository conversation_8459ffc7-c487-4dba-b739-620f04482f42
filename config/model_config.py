"""
Model Configuration Classes

This module contains configuration classes for different model sizes
and training setups, optimized for Google Colab constraints.
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any
import json
import os


@dataclass
class ModelConfig:
    """Configuration for transformer model architecture."""
    
    # Model architecture
    vocab_size: int = 50257  # GPT-2 vocab size
    max_seq_length: int = 512  # Reduced for Colab memory constraints
    d_model: int = 512  # Hidden dimension
    n_heads: int = 8  # Number of attention heads
    n_layers: int = 6  # Number of transformer blocks
    d_ff: int = 2048  # Feed-forward dimension
    dropout: float = 0.1
    
    # Training parameters
    batch_size: int = 8  # Small batch for Colab
    learning_rate: float = 3e-4
    weight_decay: float = 0.01
    warmup_steps: int = 1000
    max_steps: int = 10000
    eval_interval: int = 500
    save_interval: int = 1000
    
    # Optimization
    gradient_accumulation_steps: int = 4  # Effective batch size = 32
    max_grad_norm: float = 1.0
    use_mixed_precision: bool = True
    use_gradient_checkpointing: bool = True
    
    # Data
    train_data_path: str = "data/train.txt"
    val_data_path: str = "data/val.txt"
    tokenizer_path: str = "tokenizer/bpe_tokenizer.json"
    
    # Checkpointing
    checkpoint_dir: str = "checkpoints"
    log_dir: str = "logs"
    
    def save(self, path: str) -> None:
        """Save configuration to JSON file."""
        os.makedirs(os.path.dirname(path), exist_ok=True)
        with open(path, 'w') as f:
            json.dump(self.__dict__, f, indent=2)
    
    @classmethod
    def load(cls, path: str) -> 'ModelConfig':
        """Load configuration from JSON file."""
        with open(path, 'r') as f:
            config_dict = json.load(f)
        return cls(**config_dict)
    
    def update(self, **kwargs) -> 'ModelConfig':
        """Create new config with updated parameters."""
        config_dict = self.__dict__.copy()
        config_dict.update(kwargs)
        return ModelConfig(**config_dict)


# Predefined configurations for different model sizes
TINY_CONFIG = ModelConfig(
    d_model=256,
    n_heads=4,
    n_layers=4,
    d_ff=1024,
    max_seq_length=256,
    batch_size=16,
    max_steps=5000
)

SMALL_CONFIG = ModelConfig(
    d_model=512,
    n_heads=8,
    n_layers=6,
    d_ff=2048,
    max_seq_length=512,
    batch_size=8,
    max_steps=10000
)

MEDIUM_CONFIG = ModelConfig(
    d_model=768,
    n_heads=12,
    n_layers=8,
    d_ff=3072,
    max_seq_length=512,
    batch_size=4,
    max_steps=15000,
    gradient_accumulation_steps=8
)

# Default configuration (optimized for Colab free tier)
DEFAULT_CONFIG = SMALL_CONFIG
